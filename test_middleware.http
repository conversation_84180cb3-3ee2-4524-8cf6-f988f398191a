# FastAPI 中间件功能测试文件
# 使用 VS Code 的 REST Client 插件或 IntelliJ HTTP Client 运行这些测试

### 1. 测试基本 API 功能
GET http://localhost:8181/
Accept: application/json

### 2. 测试个性化问候
GET http://localhost:8181/hello/张三
Accept: application/json

### 3. 测试健康检查
GET http://localhost:8181/health
Accept: application/json

### 4. 测试 POST 请求和日志记录
POST http://localhost:8181/echo
Content-Type: application/json

{
  "message": "这是一个测试消息",
  "user": "测试用户",
  "timestamp": "2025-07-02"
}

### 5. 测试 CORS - 跨域请求
GET http://localhost:8181/
Origin: http://localhost:3000
Accept: application/json

### 6. 测试 CORS - 预检请求
OPTIONS http://localhost:8181/echo
Origin: http://localhost:3000
Access-Control-Request-Method: POST
Access-Control-Request-Headers: Content-Type

### 7. 测试限流功能 - 单个请求
GET http://localhost:8181/test-rate-limit
Accept: application/json

### 8. 测试限流功能 - 快速连续请求（可能触发限流）
GET http://localhost:8181/test-rate-limit

###
GET http://localhost:8181/test-rate-limit

###
GET http://localhost:8181/test-rate-limit

###
GET http://localhost:8181/test-rate-limit

###
GET http://localhost:8181/test-rate-limit

### 9. 检查安全头部
GET http://localhost:8181/
Accept: application/json

### 10. 测试 API 文档访问
GET http://localhost:8181/docs
Accept: text/html

### 11. 测试 OpenAPI 规范
GET http://localhost:8181/openapi.json
Accept: application/json

### 12. 测试错误处理（访问不存在的端点）
GET http://localhost:8181/nonexistent
Accept: application/json
