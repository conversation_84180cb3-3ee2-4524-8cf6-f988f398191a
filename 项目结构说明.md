# FastAPI 项目结构说明

## 项目目录结构

```
FastAPIProject/
├── main.py                    # 简单的 FastAPI 应用（备用）
├── server.py                  # 服务器启动脚本（主要入口）
├── test_main.http            # HTTP 测试文件
├── 项目结构说明.md           # 本文件
└── src/                      # 源代码目录
    ├── __init__.py           # 使 src 成为 Python 包
    └── server/               # 服务器模块
        ├── __init__.py       # 包初始化文件，导出 app 对象
        ├── app.py            # FastAPI 应用定义（包含中间件配置）
        └── middleware/       # 中间件模块
            ├── __init__.py           # 中间件包初始化
            ├── cors_middleware.py    # CORS 跨域中间件
            ├── rate_limit_middleware.py  # 限流中间件
            ├── logging_middleware.py     # 日志记录中间件
            └── security_middleware.py    # 安全头部中间件
```

## 文件功能说明

### 1. `server.py` - 服务器启动脚本
- **作用**: 项目的主要入口点，提供命令行接口启动服务器
- **功能**:
  - 解析命令行参数（主机、端口、重载模式、日志级别）
  - 配置日志系统
  - 启动 uvicorn ASGI 服务器
- **使用方法**:
  ```bash
  python server.py                           # 默认参数启动
  python server.py --host 0.0.0.0 --port 8080  # 自定义主机和端口
  python server.py --reload                  # 开发模式（自动重载）
  python server.py --log_level debug         # 调试模式
  ```

### 2. `src/server/app.py` - FastAPI 应用定义
- **作用**: 定义 FastAPI 应用的核心功能
- **功能**:
  - 创建 FastAPI 应用实例
  - 定义 API 路由和处理函数
  - 提供 REST API 接口
- **API 端点**:
  - `GET /` - 返回欢迎消息
  - `GET /hello/{name}` - 个性化问候

### 3. `src/server/__init__.py` - 包初始化文件
- **作用**: 将 server 目录变成 Python 包，并导出 app 对象
- **功能**:
  - 从 app.py 导入 FastAPI 应用实例
  - 定义包的公共接口（__all__）
  - 使外部可以通过 `src.server:app` 访问应用

### 4. `src/__init__.py` - 源码包初始化
- **作用**: 将 src 目录变成 Python 包
- **功能**: 使 Python 能够将 src 识别为包，支持模块导入

### 5. `main.py` - 备用应用文件
- **作用**: 简单的 FastAPI 应用，可作为备用或测试使用
- **使用方法**: `uvicorn main:app --reload`

## 工作流程

### 启动流程
1. 运行 `python server.py` 命令
2. `server.py` 解析命令行参数
3. 配置日志系统
4. 调用 `uvicorn.run("src.server:app", ...)`
5. uvicorn 查找 `src.server` 模块中的 `app` 对象
6. 通过 `src/server/__init__.py` 找到并导入 `app.py` 中的 FastAPI 应用
7. 启动 ASGI 服务器，开始监听 HTTP 请求

### 请求处理流程
1. 客户端发送 HTTP 请求到服务器
2. uvicorn 接收请求并传递给 FastAPI 应用
3. FastAPI 根据 URL 路径匹配对应的路由处理函数
4. 执行处理函数，生成响应数据
5. FastAPI 将 Python 对象转换为 JSON 响应
6. uvicorn 将响应发送回客户端

## 优势

1. **模块化设计**: 代码组织清晰，职责分离
2. **灵活配置**: 支持命令行参数自定义服务器设置
3. **开发友好**: 支持自动重载，便于开发调试
4. **标准结构**: 遵循 Python 包管理最佳实践
5. **易于扩展**: 可以轻松添加新的 API 端点和功能

## 下一步扩展建议

1. 添加数据库连接和模型定义
2. 实现用户认证和授权
3. 添加中间件（CORS、限流等）
4. 集成配置文件管理
5. 添加单元测试和集成测试
6. 实现 API 文档和版本管理
