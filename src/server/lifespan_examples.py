# -*- coding: utf-8 -*-
"""
应用生命周期管理示例

这个文件展示了如何在 FastAPI 应用的 lifespan 事件中
添加各种初始化和清理操作的实际示例。
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any
from fastapi import FastAPI

logger = logging.getLogger(__name__)

# 全局状态存储
app_state: Dict[str, Any] = {}


class DatabaseManager:
    """
    数据库连接管理器示例
    """
    
    def __init__(self):
        self.connected = False
        self.connection = None
    
    async def connect(self):
        """连接数据库"""
        logger.info("🔌 正在连接数据库...")
        # 模拟数据库连接
        await asyncio.sleep(0.1)
        self.connected = True
        self.connection = "mock_database_connection"
        logger.info("✅ 数据库连接成功")
    
    async def disconnect(self):
        """断开数据库连接"""
        if self.connected:
            logger.info("🔌 正在断开数据库连接...")
            # 模拟断开连接
            await asyncio.sleep(0.1)
            self.connected = False
            self.connection = None
            logger.info("✅ 数据库连接已断开")


class CacheManager:
    """
    缓存管理器示例
    """
    
    def __init__(self):
        self.cache = {}
        self.connected = False
    
    async def connect(self):
        """连接缓存服务"""
        logger.info("🗄️ 正在连接缓存服务...")
        await asyncio.sleep(0.1)
        self.connected = True
        logger.info("✅ 缓存服务连接成功")
    
    async def disconnect(self):
        """断开缓存连接"""
        if self.connected:
            logger.info("🗄️ 正在断开缓存连接...")
            await asyncio.sleep(0.1)
            self.cache.clear()
            self.connected = False
            logger.info("✅ 缓存连接已断开")


class BackgroundTaskManager:
    """
    后台任务管理器示例
    """
    
    def __init__(self):
        self.tasks = []
        self.running = False
    
    async def start_tasks(self):
        """启动后台任务"""
        logger.info("⚙️ 正在启动后台任务...")
        self.running = True
        
        # 启动示例后台任务
        task1 = asyncio.create_task(self._periodic_cleanup())
        task2 = asyncio.create_task(self._health_monitor())
        
        self.tasks.extend([task1, task2])
        logger.info(f"✅ 已启动 {len(self.tasks)} 个后台任务")
    
    async def stop_tasks(self):
        """停止后台任务"""
        if self.running:
            logger.info("⚙️ 正在停止后台任务...")
            self.running = False
            
            # 取消所有任务
            for task in self.tasks:
                if not task.done():
                    task.cancel()
            
            # 等待任务完成
            if self.tasks:
                await asyncio.gather(*self.tasks, return_exceptions=True)
            
            self.tasks.clear()
            logger.info("✅ 所有后台任务已停止")
    
    async def _periodic_cleanup(self):
        """定期清理任务"""
        while self.running:
            try:
                logger.debug("🧹 执行定期清理...")
                # 这里可以添加实际的清理逻辑
                await asyncio.sleep(300)  # 每5分钟执行一次
            except asyncio.CancelledError:
                logger.info("🧹 定期清理任务已取消")
                break
            except Exception as e:
                logger.error(f"🧹 定期清理任务出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试
    
    async def _health_monitor(self):
        """健康监控任务"""
        while self.running:
            try:
                logger.debug("💓 执行健康检查...")
                # 这里可以添加实际的健康检查逻辑
                await asyncio.sleep(30)  # 每30秒检查一次
            except asyncio.CancelledError:
                logger.info("💓 健康监控任务已取消")
                break
            except Exception as e:
                logger.error(f"💓 健康监控任务出错: {e}")
                await asyncio.sleep(10)  # 出错后等待10秒再重试


@asynccontextmanager
async def comprehensive_lifespan(app: FastAPI):
    """
    完整的应用生命周期管理示例
    
    展示如何在应用启动和关闭时管理各种资源
    """
    # 创建管理器实例
    db_manager = DatabaseManager()
    cache_manager = CacheManager()
    task_manager = BackgroundTaskManager()
    
    # 将管理器存储到应用状态中，供其他地方使用
    app_state["db_manager"] = db_manager
    app_state["cache_manager"] = cache_manager
    app_state["task_manager"] = task_manager
    
    try:
        # === 应用启动阶段 ===
        logger.info("🚀 开始应用初始化...")
        
        # 步骤1: 连接数据库
        await db_manager.connect()
        
        # 步骤2: 连接缓存服务
        await cache_manager.connect()
        
        # 步骤3: 启动后台任务
        await task_manager.start_tasks()
        
        # 步骤4: 预热应用（可选）
        logger.info("🔥 正在预热应用...")
        # 这里可以添加预热逻辑，如预加载数据、预编译模板等
        await asyncio.sleep(0.1)
        
        # 步骤5: 验证所有服务状态
        if (db_manager.connected and 
            cache_manager.connected and 
            task_manager.running):
            logger.info("✅ 所有服务初始化完成")
            app_state["startup_time"] = asyncio.get_event_loop().time()
        else:
            raise RuntimeError("部分服务初始化失败")
        
        logger.info("🎉 应用启动完成，准备处理请求")
        
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        # 清理已初始化的资源
        await cleanup_resources(db_manager, cache_manager, task_manager)
        raise
    
    # yield 分隔启动和关闭逻辑
    yield
    
    # === 应用关闭阶段 ===
    logger.info("🛑 开始应用关闭流程...")
    await cleanup_resources(db_manager, cache_manager, task_manager)
    logger.info("👋 应用已安全关闭")


async def cleanup_resources(db_manager, cache_manager, task_manager):
    """
    清理应用资源
    
    按照相反的顺序清理资源，确保依赖关系正确
    """
    try:
        # 步骤1: 停止后台任务（最先停止，避免在清理过程中继续工作）
        await task_manager.stop_tasks()
        
        # 步骤2: 断开缓存连接
        await cache_manager.disconnect()
        
        # 步骤3: 断开数据库连接（最后断开，因为其他服务可能依赖它）
        await db_manager.disconnect()
        
        # 步骤4: 清理应用状态
        app_state.clear()
        
        logger.info("✅ 所有资源清理完成")
        
    except Exception as e:
        logger.error(f"❌ 资源清理时出错: {e}")


# 使用示例：
# 在 app.py 中，将 lifespan=lifespan 改为 lifespan=comprehensive_lifespan
# 就可以使用这个更完整的生命周期管理

def get_app_state() -> Dict[str, Any]:
    """
    获取应用状态
    
    其他模块可以通过这个函数访问应用状态
    """
    return app_state


def get_database():
    """
    获取数据库连接
    
    在路由处理函数中可以使用这个函数获取数据库连接
    """
    return app_state.get("db_manager")


def get_cache():
    """
    获取缓存管理器
    
    在路由处理函数中可以使用这个函数获取缓存
    """
    return app_state.get("cache_manager")

# 生命周期管理的最佳实践：
# 1. 按照依赖关系的顺序初始化资源
# 2. 按照相反的顺序清理资源
# 3. 使用 try-except 处理初始化和清理过程中的异常
# 4. 将资源管理器存储到应用状态中，供其他地方使用
# 5. 提供便捷的访问函数来获取资源
