# -*- coding: utf-8 -*-
"""
中间件模块

这个包包含了 FastAPI 应用的各种中间件：
- CORS 中间件：处理跨域请求
- 限流中间件：防止 API 滥用
- 日志中间件：记录请求和响应
- 安全中间件：添加安全头
"""

from .cors_middleware import setup_cors
from .rate_limit_middleware import setup_rate_limit
from .logging_middleware import setup_logging
from .security_middleware import setup_security

__all__ = [
    "setup_cors",
    "setup_rate_limit", 
    "setup_logging",
    "setup_security"
]
