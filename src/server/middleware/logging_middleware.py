# -*- coding: utf-8 -*-
"""
日志中间件

记录 HTTP 请求和响应的详细信息，用于监控、调试和审计。
包括请求时间、响应时间、状态码、用户代理等信息。
"""

import time
import logging
import json
from typing import Optional
from fastapi import FastAPI, Request
from fastapi.responses import Response

logger = logging.getLogger(__name__)


def get_client_info(request: Request) -> dict:
    """
    获取客户端信息
    
    参数:
        request (Request): FastAPI 请求对象
        
    返回:
        dict: 包含客户端信息的字典
    """
    # 获取客户端 IP 地址（考虑代理情况）
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        client_ip = forwarded_for.split(",")[0].strip()
    else:
        real_ip = request.headers.get("X-Real-IP")
        client_ip = real_ip or (request.client.host if request.client else "unknown")
    
    return {
        "ip": client_ip,
        "user_agent": request.headers.get("User-Agent", "unknown"),
        "referer": request.headers.get("Referer"),
        "accept_language": request.headers.get("Accept-Language"),
        "accept_encoding": request.headers.get("Accept-Encoding"),
    }


def format_request_size(content_length: Optional[str]) -> str:
    """
    格式化请求大小
    
    参数:
        content_length (Optional[str]): Content-Length 头部值
        
    返回:
        str: 格式化的大小字符串
    """
    if not content_length:
        return "0 B"
    
    try:
        size = int(content_length)
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        else:
            return f"{size / (1024 * 1024):.1f} MB"
    except (ValueError, TypeError):
        return "unknown"


def should_log_request(request: Request) -> bool:
    """
    判断是否应该记录该请求
    
    某些请求（如健康检查、静态资源）可能不需要详细记录
    
    参数:
        request (Request): 请求对象
        
    返回:
        bool: 是否应该记录
    """
    path = request.url.path
    
    # 跳过的路径模式
    skip_patterns = [
        "/health",      # 健康检查
        "/metrics",     # 监控指标
        "/favicon.ico", # 网站图标
        "/robots.txt",  # 爬虫协议
    ]
    
    # 检查是否匹配跳过模式
    for pattern in skip_patterns:
        if path.startswith(pattern):
            return False
    
    return True


async def logging_middleware(request: Request, call_next):
    """
    日志记录中间件
    
    参数:
        request (Request): 请求对象
        call_next: 下一个中间件或路由处理函数
        
    返回:
        Response: 响应对象
    """
    # 步骤1: 记录请求开始时间
    start_time = time.time()
    
    # 步骤2: 获取请求信息
    client_info = get_client_info(request)
    request_size = format_request_size(request.headers.get("Content-Length"))
    
    # 步骤3: 记录请求开始日志
    if should_log_request(request):
        logger.info(
            f"请求开始 - {request.method} {request.url.path} "
            f"来自 {client_info['ip']} ({client_info['user_agent'][:50]}...)"
        )
    
    # 步骤4: 处理请求并捕获可能的异常
    try:
        response = await call_next(request)
        
        # 步骤5: 计算处理时间
        process_time = time.time() - start_time
        
        # 步骤6: 记录响应日志
        if should_log_request(request):
            # 获取响应大小
            response_size = "unknown"
            if hasattr(response, 'headers') and 'content-length' in response.headers:
                response_size = format_request_size(response.headers['content-length'])
            
            # 根据状态码确定日志级别
            if response.status_code >= 500:
                log_level = logging.ERROR
                log_message = "服务器错误"
            elif response.status_code >= 400:
                log_level = logging.WARNING
                log_message = "客户端错误"
            elif response.status_code >= 300:
                log_level = logging.INFO
                log_message = "重定向"
            else:
                log_level = logging.INFO
                log_message = "成功"
            
            # 记录详细的响应日志
            logger.log(
                log_level,
                f"{log_message} - {request.method} {request.url.path} "
                f"状态码: {response.status_code} "
                f"处理时间: {process_time:.3f}s "
                f"请求大小: {request_size} "
                f"响应大小: {response_size} "
                f"客户端: {client_info['ip']}"
            )
            
            # 如果是错误响应，记录更多详细信息
            if response.status_code >= 400:
                logger.debug(
                    f"错误详情 - 路径: {request.url.path} "
                    f"查询参数: {dict(request.query_params)} "
                    f"头部: {dict(request.headers)} "
                    f"客户端信息: {json.dumps(client_info, ensure_ascii=False)}"
                )
        
        # 步骤7: 添加性能相关的响应头
        if hasattr(response, 'headers'):
            response.headers["X-Process-Time"] = f"{process_time:.3f}"
            response.headers["X-Timestamp"] = str(int(start_time))
        
        return response
        
    except Exception as e:
        # 步骤8: 处理异常情况
        process_time = time.time() - start_time
        
        logger.error(
            f"请求处理异常 - {request.method} {request.url.path} "
            f"错误: {str(e)} "
            f"处理时间: {process_time:.3f}s "
            f"客户端: {client_info['ip']}"
        )
        
        # 重新抛出异常，让 FastAPI 的异常处理器处理
        raise


def setup_logging(app: FastAPI, 
                 log_level: str = "INFO",
                 include_request_body: bool = False,
                 include_response_body: bool = False) -> None:
    """
    配置日志中间件
    
    参数:
        app (FastAPI): FastAPI 应用实例
        log_level (str): 日志级别
        include_request_body (bool): 是否记录请求体（敏感信息需谨慎）
        include_response_body (bool): 是否记录响应体（可能影响性能）
    """
    # 步骤1: 设置日志级别
    logging.getLogger(__name__).setLevel(getattr(logging, log_level.upper()))
    
    # 步骤2: 添加中间件到应用
    app.middleware("http")(logging_middleware)
    
    # 步骤3: 配置额外的日志格式器（如果需要）
    if include_request_body or include_response_body:
        logger.warning("启用了请求/响应体记录，请确保不会记录敏感信息")
    
    logger.info(f"日志中间件已配置，日志级别: {log_level}")


def setup_structured_logging(app: FastAPI) -> None:
    """
    配置结构化日志记录
    
    使用 JSON 格式记录日志，便于日志分析工具处理
    
    参数:
        app (FastAPI): FastAPI 应用实例
    """
    import json_logging
    
    # 启用 JSON 日志格式
    json_logging.init_fastapi(enable_json=True)
    json_logging.init_request_instrument(app)
    
    logger.info("结构化日志记录已启用")


def setup_access_log_file(app: FastAPI, log_file_path: str = "access.log") -> None:
    """
    配置访问日志文件
    
    将访问日志单独记录到文件中
    
    参数:
        app (FastAPI): FastAPI 应用实例
        log_file_path (str): 日志文件路径
    """
    import logging.handlers
    
    # 创建文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_file_path,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    
    # 添加到日志记录器
    logger.addHandler(file_handler)
    
    logger.info(f"访问日志将记录到文件: {log_file_path}")

# 日志中间件的作用：
# 1. 监控：跟踪应用的使用情况和性能
# 2. 调试：帮助开发者定位问题
# 3. 审计：记录重要操作的历史
# 4. 分析：为业务分析提供数据支持
# 5. 安全：检测异常访问模式
