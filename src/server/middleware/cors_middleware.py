# -*- coding: utf-8 -*-
"""
CORS 中间件配置

CORS (Cross-Origin Resource Sharing) 跨域资源共享中间件
用于处理来自不同域名的前端应用的请求。
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

logger = logging.getLogger(__name__)


def setup_cors(app: FastAPI, 
               allow_origins: list = None,
               allow_credentials: bool = True,
               allow_methods: list = None,
               allow_headers: list = None) -> None:
    """
    配置 CORS 中间件
    
    参数:
        app (FastAPI): FastAPI 应用实例
        allow_origins (list): 允许的源域名列表，默认为开发环境常用域名
        allow_credentials (bool): 是否允许携带凭证（cookies等），默认为 True
        allow_methods (list): 允许的 HTTP 方法列表，默认为常用方法
        allow_headers (list): 允许的请求头列表，默认为常用头部
    """
    
    # 步骤1: 设置默认的允许源域名
    # 在开发环境中通常需要允许本地前端应用的访问
    if allow_origins is None:
        allow_origins = [
            "http://localhost:3000",    # React 默认端口
            "http://localhost:8080",    # Vue.js 默认端口
            "http://localhost:5173",    # Vite 默认端口
            "http://127.0.0.1:3000",
            "http://127.0.0.1:8080", 
            "http://127.0.0.1:5173",
            # 生产环境中应该指定具体的域名
            # "https://yourdomain.com",
        ]
    
    # 步骤2: 设置默认的允许方法
    # 包含常用的 HTTP 方法
    if allow_methods is None:
        allow_methods = [
            "GET",      # 获取资源
            "POST",     # 创建资源
            "PUT",      # 更新资源（完整更新）
            "PATCH",    # 更新资源（部分更新）
            "DELETE",   # 删除资源
            "OPTIONS",  # 预检请求
            "HEAD",     # 获取头部信息
        ]
    
    # 步骤3: 设置默认的允许头部
    # 包含常用的请求头
    if allow_headers is None:
        allow_headers = [
            "Accept",           # 客户端接受的内容类型
            "Accept-Language",  # 客户端接受的语言
            "Content-Type",     # 请求体的内容类型
            "Content-Language", # 请求体的语言
            "Authorization",    # 认证信息
            "X-Requested-With", # AJAX 请求标识
            "X-CSRF-Token",     # CSRF 防护令牌
            "Cache-Control",    # 缓存控制
        ]
    
    # 步骤4: 添加 CORS 中间件到 FastAPI 应用
    app.add_middleware(
        CORSMiddleware,
        allow_origins=allow_origins,        # 允许的源域名
        allow_credentials=allow_credentials, # 是否允许凭证
        allow_methods=allow_methods,        # 允许的方法
        allow_headers=allow_headers,        # 允许的头部
    )
    
    # 步骤5: 记录 CORS 配置信息
    logger.info("CORS 中间件已配置")
    logger.info(f"允许的源域名: {allow_origins}")
    logger.info(f"允许凭证: {allow_credentials}")
    logger.info(f"允许的方法: {allow_methods}")
    logger.info(f"允许的头部: {allow_headers}")


def setup_cors_for_production(app: FastAPI, production_origins: list) -> None:
    """
    为生产环境配置更严格的 CORS 策略
    
    参数:
        app (FastAPI): FastAPI 应用实例
        production_origins (list): 生产环境允许的域名列表
    """
    setup_cors(
        app=app,
        allow_origins=production_origins,  # 只允许指定的生产域名
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],  # 限制方法
        allow_headers=["Content-Type", "Authorization"]   # 限制头部
    )
    
    logger.info("生产环境 CORS 策略已应用")


def setup_cors_for_development(app: FastAPI) -> None:
    """
    为开发环境配置宽松的 CORS 策略
    
    参数:
        app (FastAPI): FastAPI 应用实例
    """
    setup_cors(
        app=app,
        allow_origins=["*"],  # 允许所有域名（仅开发环境）
        allow_credentials=False,  # 使用通配符时必须为 False
        allow_methods=["*"],  # 允许所有方法
        allow_headers=["*"]   # 允许所有头部
    )
    
    logger.warning("开发环境 CORS 策略已应用 - 允许所有源访问")

# CORS 工作原理说明：
# 1. 浏览器在发送跨域请求前，会先发送 OPTIONS 预检请求
# 2. 服务器返回允许的域名、方法、头部等信息
# 3. 浏览器根据服务器响应决定是否发送实际请求
# 4. CORS 中间件自动处理这些预检请求和响应头设置
