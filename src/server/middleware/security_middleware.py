# -*- coding: utf-8 -*-
"""
安全中间件

添加各种安全相关的 HTTP 头部，提高应用的安全性。
包括 HTTPS 强制、XSS 防护、内容类型嗅探防护等。
"""

import logging
from fastapi import FastAPI, Request
from fastapi.responses import Response

logger = logging.getLogger(__name__)


class SecurityHeaders:
    """
    安全头部配置类
    
    定义各种安全相关的 HTTP 头部
    """
    
    def __init__(self):
        """初始化默认的安全头部配置"""
        
        # 步骤1: HTTPS 相关头部
        self.hsts_header = "max-age=31536000; includeSubDomains"  # 强制 HTTPS 一年
        
        # 步骤2: XSS 防护
        self.xss_protection = "1; mode=block"  # 启用 XSS 过滤器
        
        # 步骤3: 内容类型嗅探防护
        self.content_type_options = "nosniff"  # 禁止 MIME 类型嗅探
        
        # 步骤4: 点击劫持防护
        self.frame_options = "DENY"  # 禁止在框架中显示
        
        # 步骤5: 内容安全策略（CSP）
        self.content_security_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )
        
        # 步骤6: 权限策略
        self.permissions_policy = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )
        
        # 步骤7: 引用者策略
        self.referrer_policy = "strict-origin-when-cross-origin"
        
        # 步骤8: 服务器信息隐藏
        self.server_header = "FastAPI"  # 隐藏详细的服务器信息


def add_security_headers(response: Response, headers: SecurityHeaders, 
                        is_https: bool = False) -> None:
    """
    添加安全头部到响应
    
    参数:
        response (Response): 响应对象
        headers (SecurityHeaders): 安全头部配置
        is_https (bool): 是否为 HTTPS 连接
    """
    
    # 步骤1: 添加 XSS 防护头部
    response.headers["X-XSS-Protection"] = headers.xss_protection
    
    # 步骤2: 添加内容类型嗅探防护
    response.headers["X-Content-Type-Options"] = headers.content_type_options
    
    # 步骤3: 添加点击劫持防护
    response.headers["X-Frame-Options"] = headers.frame_options
    
    # 步骤4: 添加内容安全策略
    response.headers["Content-Security-Policy"] = headers.content_security_policy
    
    # 步骤5: 添加权限策略
    response.headers["Permissions-Policy"] = headers.permissions_policy
    
    # 步骤6: 添加引用者策略
    response.headers["Referrer-Policy"] = headers.referrer_policy
    
    # 步骤7: 隐藏服务器信息
    response.headers["Server"] = headers.server_header
    
    # 步骤8: 如果是 HTTPS，添加 HSTS 头部
    if is_https:
        response.headers["Strict-Transport-Security"] = headers.hsts_header
    
    # 步骤9: 添加其他安全头部
    response.headers["X-Permitted-Cross-Domain-Policies"] = "none"
    response.headers["Cross-Origin-Embedder-Policy"] = "require-corp"
    response.headers["Cross-Origin-Opener-Policy"] = "same-origin"
    response.headers["Cross-Origin-Resource-Policy"] = "same-origin"


def is_https_request(request: Request) -> bool:
    """
    检查请求是否为 HTTPS
    
    考虑代理服务器的情况
    
    参数:
        request (Request): 请求对象
        
    返回:
        bool: 是否为 HTTPS 请求
    """
    # 步骤1: 检查直接的 HTTPS 连接
    if request.url.scheme == "https":
        return True
    
    # 步骤2: 检查代理头部
    forwarded_proto = request.headers.get("X-Forwarded-Proto")
    if forwarded_proto and forwarded_proto.lower() == "https":
        return True
    
    # 步骤3: 检查其他代理头部
    forwarded_ssl = request.headers.get("X-Forwarded-SSL")
    if forwarded_ssl and forwarded_ssl.lower() == "on":
        return True
    
    return False


async def security_middleware(request: Request, call_next):
    """
    安全中间件函数
    
    参数:
        request (Request): 请求对象
        call_next: 下一个中间件或路由处理函数
        
    返回:
        Response: 响应对象
    """
    # 步骤1: 检查是否为 HTTPS 请求
    is_https = is_https_request(request)
    
    # 步骤2: 处理请求
    response = await call_next(request)
    
    # 步骤3: 添加安全头部
    security_headers = SecurityHeaders()
    add_security_headers(response, security_headers, is_https)
    
    # 步骤4: 记录安全相关信息
    if not is_https and request.url.hostname not in ["localhost", "127.0.0.1"]:
        logger.warning(f"非 HTTPS 请求: {request.url}")
    
    return response


def setup_security(app: FastAPI, 
                  custom_csp: str = None,
                  enable_hsts: bool = True,
                  hsts_max_age: int = 31536000) -> None:
    """
    配置安全中间件
    
    参数:
        app (FastAPI): FastAPI 应用实例
        custom_csp (str): 自定义内容安全策略
        enable_hsts (bool): 是否启用 HSTS
        hsts_max_age (int): HSTS 最大年龄（秒）
    """
    # 步骤1: 创建安全头部配置
    security_headers = SecurityHeaders()
    
    # 步骤2: 应用自定义配置
    if custom_csp:
        security_headers.content_security_policy = custom_csp
    
    if not enable_hsts:
        security_headers.hsts_header = None
    elif hsts_max_age != 31536000:
        security_headers.hsts_header = f"max-age={hsts_max_age}; includeSubDomains"
    
    # 步骤3: 添加中间件到应用
    app.middleware("http")(security_middleware)
    
    logger.info("安全中间件已配置")
    logger.info(f"HSTS 启用: {enable_hsts}")
    if custom_csp:
        logger.info("使用自定义 CSP 策略")


def setup_security_for_api(app: FastAPI) -> None:
    """
    为 API 应用配置安全策略
    
    API 应用通常不需要某些浏览器安全策略
    
    参数:
        app (FastAPI): FastAPI 应用实例
    """
    # API 专用的 CSP 策略
    api_csp = (
        "default-src 'none'; "
        "frame-ancestors 'none'; "
        "base-uri 'none';"
    )
    
    setup_security(app, custom_csp=api_csp)
    logger.info("API 安全策略已应用")


def setup_security_for_web(app: FastAPI) -> None:
    """
    为 Web 应用配置安全策略
    
    Web 应用需要更宽松的 CSP 策略以支持前端资源
    
    参数:
        app (FastAPI): FastAPI 应用实例
    """
    # Web 应用的 CSP 策略
    web_csp = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
        "img-src 'self' data: https:; "
        "font-src 'self' https://fonts.gstatic.com; "
        "connect-src 'self' https:; "
        "frame-ancestors 'none';"
    )
    
    setup_security(app, custom_csp=web_csp)
    logger.info("Web 应用安全策略已应用")


# 安全头部说明：
# 1. X-XSS-Protection: 启用浏览器的 XSS 过滤器
# 2. X-Content-Type-Options: 防止 MIME 类型嗅探攻击
# 3. X-Frame-Options: 防止点击劫持攻击
# 4. Content-Security-Policy: 控制资源加载，防止 XSS
# 5. Strict-Transport-Security: 强制使用 HTTPS
# 6. Permissions-Policy: 控制浏览器功能访问权限
# 7. Referrer-Policy: 控制引用者信息的发送
