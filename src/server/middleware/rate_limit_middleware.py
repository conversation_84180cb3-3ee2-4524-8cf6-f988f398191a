# -*- coding: utf-8 -*-
"""
限流中间件

实现基于 IP 地址的请求频率限制，防止 API 滥用和 DDoS 攻击。
使用滑动窗口算法进行限流控制。
"""

import time
import logging
from collections import defaultdict, deque
from typing import Dict, Deque, Tu<PERSON>
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
import asyncio

logger = logging.getLogger(__name__)


class RateLimiter:
    """
    滑动窗口限流器
    
    使用滑动窗口算法跟踪每个 IP 地址的请求频率
    """
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        """
        初始化限流器
        
        参数:
            max_requests (int): 时间窗口内允许的最大请求数，默认 100
            window_seconds (int): 时间窗口大小（秒），默认 60 秒
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        # 存储每个 IP 的请求时间戳队列
        self.requests: Dict[str, Deque[float]] = defaultdict(deque)
        # 用于清理过期数据的锁
        self._cleanup_lock = asyncio.Lock()
        
        logger.info(f"限流器初始化: {max_requests} 请求/{window_seconds} 秒")
    
    async def is_allowed(self, client_ip: str) -> Tuple[bool, int]:
        """
        检查是否允许请求
        
        参数:
            client_ip (str): 客户端 IP 地址
            
        返回:
            Tuple[bool, int]: (是否允许, 剩余请求数)
        """
        current_time = time.time()
        
        # 获取该 IP 的请求记录
        ip_requests = self.requests[client_ip]
        
        # 清理过期的请求记录（超出时间窗口的记录）
        while ip_requests and current_time - ip_requests[0] > self.window_seconds:
            ip_requests.popleft()
        
        # 检查当前请求数是否超过限制
        current_requests = len(ip_requests)
        
        if current_requests >= self.max_requests:
            # 超过限制，拒绝请求
            remaining = 0
            allowed = False
            logger.warning(f"IP {client_ip} 超过限流限制: {current_requests}/{self.max_requests}")
        else:
            # 允许请求，记录当前请求时间
            ip_requests.append(current_time)
            remaining = self.max_requests - current_requests - 1
            allowed = True
        
        return allowed, remaining
    
    async def cleanup_expired_records(self):
        """
        定期清理过期的请求记录，防止内存泄漏
        """
        async with self._cleanup_lock:
            current_time = time.time()
            expired_ips = []
            
            for ip, requests in self.requests.items():
                # 清理过期记录
                while requests and current_time - requests[0] > self.window_seconds:
                    requests.popleft()
                
                # 如果该 IP 没有活跃请求，标记为可删除
                if not requests:
                    expired_ips.append(ip)
            
            # 删除没有活跃请求的 IP 记录
            for ip in expired_ips:
                del self.requests[ip]
            
            if expired_ips:
                logger.debug(f"清理了 {len(expired_ips)} 个过期 IP 记录")


# 全局限流器实例
_rate_limiter: RateLimiter = None


def get_client_ip(request: Request) -> str:
    """
    获取客户端真实 IP 地址
    
    考虑代理服务器和负载均衡器的情况
    
    参数:
        request (Request): FastAPI 请求对象
        
    返回:
        str: 客户端 IP 地址
    """
    # 步骤1: 检查常见的代理头部
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For 可能包含多个 IP，取第一个（客户端真实 IP）
        return forwarded_for.split(",")[0].strip()
    
    # 步骤2: 检查其他代理头部
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    # 步骤3: 使用直连 IP
    return request.client.host if request.client else "unknown"


async def rate_limit_middleware(request: Request, call_next):
    """
    限流中间件函数
    
    参数:
        request (Request): 请求对象
        call_next: 下一个中间件或路由处理函数
        
    返回:
        Response: 响应对象
    """
    global _rate_limiter
    
    if _rate_limiter is None:
        # 如果限流器未初始化，直接通过
        return await call_next(request)
    
    # 步骤1: 获取客户端 IP
    client_ip = get_client_ip(request)
    
    # 步骤2: 检查是否允许请求
    allowed, remaining = await _rate_limiter.is_allowed(client_ip)
    
    if not allowed:
        # 步骤3: 如果超过限制，返回 429 错误
        logger.warning(f"限流拒绝请求: IP={client_ip}, 路径={request.url.path}")
        
        return JSONResponse(
            status_code=429,
            content={
                "error": "请求过于频繁",
                "message": f"每 {_rate_limiter.window_seconds} 秒最多允许 {_rate_limiter.max_requests} 个请求",
                "retry_after": _rate_limiter.window_seconds
            },
            headers={
                "Retry-After": str(_rate_limiter.window_seconds),
                "X-RateLimit-Limit": str(_rate_limiter.max_requests),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(int(time.time() + _rate_limiter.window_seconds))
            }
        )
    
    # 步骤4: 处理请求
    response = await call_next(request)
    
    # 步骤5: 添加限流信息到响应头
    response.headers["X-RateLimit-Limit"] = str(_rate_limiter.max_requests)
    response.headers["X-RateLimit-Remaining"] = str(remaining)
    response.headers["X-RateLimit-Reset"] = str(int(time.time() + _rate_limiter.window_seconds))
    
    return response


def setup_rate_limit(app: FastAPI, 
                    max_requests: int = 100, 
                    window_seconds: int = 60,
                    enable_cleanup: bool = True) -> None:
    """
    配置限流中间件
    
    参数:
        app (FastAPI): FastAPI 应用实例
        max_requests (int): 时间窗口内允许的最大请求数
        window_seconds (int): 时间窗口大小（秒）
        enable_cleanup (bool): 是否启用定期清理
    """
    global _rate_limiter
    
    # 步骤1: 创建限流器实例
    _rate_limiter = RateLimiter(max_requests, window_seconds)
    
    # 步骤2: 添加中间件到应用
    app.middleware("http")(rate_limit_middleware)
    
    # 步骤3: 启用定期清理任务
    if enable_cleanup:
        @app.on_event("startup")
        async def start_cleanup_task():
            """启动清理任务"""
            asyncio.create_task(periodic_cleanup())
    
    logger.info(f"限流中间件已配置: {max_requests} 请求/{window_seconds} 秒")


async def periodic_cleanup():
    """
    定期清理过期记录的后台任务
    """
    global _rate_limiter
    
    while True:
        try:
            if _rate_limiter:
                await _rate_limiter.cleanup_expired_records()
            # 每 5 分钟清理一次
            await asyncio.sleep(300)
        except Exception as e:
            logger.error(f"清理任务出错: {e}")
            await asyncio.sleep(60)  # 出错后等待 1 分钟再重试

# 限流算法说明：
# 1. 滑动窗口算法：维护每个 IP 在时间窗口内的请求时间戳
# 2. 请求到达时，清理过期的时间戳
# 3. 检查当前窗口内的请求数是否超过限制
# 4. 如果超过限制，拒绝请求并返回 429 状态码
# 5. 定期清理过期数据，防止内存泄漏
