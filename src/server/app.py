# -*- coding: utf-8 -*-
"""
FastAPI 应用程序主文件

这个文件定义了 FastAPI 应用程序的核心功能，包括：
- 创建 FastAPI 应用实例
- 配置中间件（CORS、限流、日志、安全）
- 定义 API 路由和处理函数
"""

import logging
from fastapi import FastAPI

# 导入中间件配置函数
from .middleware import (
    setup_cors,
    setup_rate_limit,
    setup_logging,
    setup_security
)

# 配置日志
logger = logging.getLogger(__name__)

# 步骤1: 创建 FastAPI 应用实例
# 添加应用的基本信息和文档配置
app = FastAPI(
    title="FastAPI 聊天机器人 API",
    description="一个功能完整的 FastAPI 应用，包含 CORS、限流、日志和安全中间件",
    version="1.0.0",
    docs_url="/docs",      # Swagger UI 文档地址
    redoc_url="/redoc",    # ReDoc 文档地址
    openapi_url="/openapi.json"  # OpenAPI 规范地址
)

# 步骤2: 配置中间件
# 注意：中间件的添加顺序很重要，后添加的中间件会先执行

# 2.1 配置安全中间件（最先执行，添加安全头部）
setup_security(app)

# 2.2 配置 CORS 中间件（处理跨域请求）
setup_cors(app)

# 2.3 配置限流中间件（防止 API 滥用）
setup_rate_limit(
    app,
    max_requests=100,    # 每分钟最多 100 个请求
    window_seconds=60    # 时间窗口为 60 秒
)

# 2.4 配置日志中间件（记录请求和响应信息）
setup_logging(app, log_level="INFO")

logger.info("FastAPI 应用和中间件初始化完成")


# 步骤3: 定义根路径的 GET 请求处理函数
# @app.get("/") 是一个装饰器，用于注册 HTTP GET 请求的路由
# "/" 表示根路径，即访问 http://localhost:8000/ 时会调用这个函数
@app.get("/")
async def root():
    """
    根路径处理函数

    返回:
        dict: 包含欢迎消息的字典
    """
    # 返回 JSON 格式的响应
    # FastAPI 会自动将 Python 字典转换为 JSON 响应
    return {"message": "Hello World"}


# 步骤4: 定义带路径参数的 GET 请求处理函数
# {name} 是路径参数，会从 URL 中提取并传递给函数
# 例如访问 http://localhost:8000/hello/张三 时，name 参数的值就是 "张三"
@app.get("/hello/{name}")
async def say_hello(name: str):
    """
    个性化问候处理函数

    参数:
        name (str): 从 URL 路径中提取的用户名

    返回:
        dict: 包含个性化问候消息的字典
    """
    # 使用 f-string 格式化字符串，将用户名插入到消息中
    # 返回个性化的问候消息
    return {"message": f"Hello {name}"}

# 整个应用的工作流程：
# 1. 当运行 uvicorn src.server:app 时，uvicorn 会查找 src.server 模块中的 app 对象
# 2. 通过 __init__.py 文件，src.server.app 指向这里定义的 FastAPI 应用实例
# 3. uvicorn 启动 ASGI 服务器，监听 HTTP 请求
# 4. 当收到请求时，FastAPI 根据 URL 路径匹配对应的路由处理函数
# 5. 执行处理函数并返回 JSON 响应给客户端