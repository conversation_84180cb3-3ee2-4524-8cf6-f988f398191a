# -*- coding: utf-8 -*-
"""
src/server 包的初始化文件

这个文件的作用是将 src/server 目录变成一个 Python 包，
并且定义了当其他模块导入这个包时可以访问的内容。
"""

# 步骤1: 从当前包的 app.py 模块中导入 app 对象
# 使用相对导入 (.app) 表示从同一个包内的 app.py 文件导入
# 这里的 app 是 FastAPI 应用实例
from .app import app

# 步骤2: 定义 __all__ 列表
# __all__ 是一个特殊变量，用于控制 "from package import *" 时导入的内容
# 只有在 __all__ 列表中的名称才会被导入
# 这是一个最佳实践，可以明确指定包的公共接口
__all__ = ["app"]

# 整个过程的解释：
# 1. 当 Python 解释器遇到 "import src.server" 或 "from src.server import app" 时
# 2. 它会首先执行这个 __init__.py 文件
# 3. 执行 "from .app import app" 语句，从 app.py 文件导入 FastAPI 应用
# 4. 设置 __all__ 列表，明确指定可以被外部访问的对象
# 5. 这样外部就可以通过 "src.server.app" 或 "from src.server import app" 来访问 FastAPI 应用了
