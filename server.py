import argparse
import logging

import args

# 日志配置信息
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Server for the chatbot')
    parser.add_argument('--host', type=str, default='localhost', help='Host to bind the server to (default: localhost)')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind the server to (default: 8000)')
    args = parser.parse_args()


    try:
        logger.info(f"Starting server on {args.host}:{args.port}")
    except Exception as e:
        logger.error(f"Failed to start server: {str(e)}")
