# -*- coding: utf-8 -*-
"""
FastAPI 服务器启动脚本

这个脚本提供了一个命令行接口来启动 FastAPI 应用程序，
支持自定义主机、端口、重载模式和日志级别等参数。
"""

# 步骤1: 导入必要的模块
import argparse  # 用于解析命令行参数
import logging   # 用于日志记录

import args      # 自定义的参数模块（如果存在）
import uvicorn   # ASGI 服务器，用于运行 FastAPI 应用

# 步骤2: 配置日志系统
# 设置日志的基本配置，包括日志级别和输出格式
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别为 INFO
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',  # 日志格式：时间 - 模块名 - 级别 - 消息
)
# 创建当前模块的日志记录器
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Server for the chatbot')
    parser.add_argument(
        '--reload',
        action='store_true',
        help='Reload the server'
    )
    parser.add_argument(
        '--host',
        type=str,
        default='localhost',
        help='Host to bind the server to (default: localhost)')
    parser.add_argument(
        '--port',
        type=int,
        default=8000,
        help='Port to bind the server to (default: 8000)'
    )
    parser.add_argument(
        '--log_level',
        type=str,
        default='info',
        choices=['debug', 'info', 'warning', 'verror', 'critical'],
        help='Log level (default: info)'
    )
    args = parser.parse_args()

    try:
        logger.info(f"Starting server on {args.host}:{args.port}")
        uvicorn.run(
            "src.server:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level
        )
    except Exception as e:
        logger.error(f"Failed to start server: {str(e)}")
